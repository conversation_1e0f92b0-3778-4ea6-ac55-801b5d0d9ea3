* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background-color: #f5f5f4;
  min-height: 100vh;
  padding: 2rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #44403c;
  margin-bottom: 0.5rem;
}

.header p {
  color: #78716c;
  font-size: 1rem;
}

.cards-container {
  position: relative;
  min-height: 500px;
}

.card {
  position: absolute;
  width: 100%;
  max-width: 600px;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 2px solid #e7e5e4;
  overflow: hidden;
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.card.selected {
  transform: translateX(60%) scale(1.05);
  width: 500px;
  max-width: 500px;
  z-index: 100 !important;
}

.card-header {
  padding: 1.25rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border-bottom: 2px solid #e7e5e4;
  transition: all 0.2s ease;
  clip-path: polygon(
    0 12px,
    24px 0,
    48px 12px,
    72px 0,
    96px 12px,
    120px 0,
    144px 12px,
    168px 0,
    192px 12px,
    216px 0,
    240px 12px,
    264px 0,
    288px 12px,
    312px 0,
    336px 12px,
    360px 0,
    384px 12px,
    408px 0,
    432px 12px,
    456px 0,
    480px 12px,
    504px 0,
    528px 12px,
    552px 0,
    576px 12px,
    600px 0,
    624px 12px,
    648px 0,
    672px 12px,
    696px 0,
    720px 12px,
    744px 0,
    768px 12px,
    792px 0,
    816px 12px,
    100% 0,
    100% 100%,
    0 100%
  );
}

.card-header:hover {
  filter: brightness(1.1);
  transform: scale(1.02);
}

.card-header:active {
  transform: scale(0.98);
}

.card.selected .card-header {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stage-name {
  font-weight: bold;
  font-size: 1.25rem;
  letter-spacing: 0.05em;
}

.stage-title {
  font-weight: 600;
  font-size: 1.125rem;
  text-align: right;
}

.indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 0.5rem;
  height: 0.5rem;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.close-btn {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  color: currentColor;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.2);
}

.card-content {
  padding: 1.5rem;
  background: white;
  min-height: 400px;
  display: none;
}

.card.selected .card-content {
  display: block;
  animation: fadeIn 0.7s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.description {
  color: #78716c;
  margin-bottom: 1rem;
  line-height: 1.6;
  font-size: 1.125rem;
}

.files-section h4 {
  font-weight: 600;
  color: #44403c;
  margin-bottom: 0.75rem;
  font-size: 1.125rem;
}

.files-list {
  max-height: 24rem;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #fafaf9;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  transition: background-color 0.2s ease;
}

.file-item:hover {
  background: #f5f5f4;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.file-icon {
  font-size: 1rem;
  color: #78716c;
}

.file-name {
  font-weight: 500;
  color: #44403c;
  margin-bottom: 0.125rem;
}

.file-meta {
  font-size: 0.875rem;
  color: #78716c;
}

.file-actions {
  display: flex;
  gap: 0.25rem;
}

.action-btn {
  width: 2rem;
  height: 2rem;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.action-btn:hover {
  background: #e7e5e4;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  animation: fadeIn 0.5s ease;
}

.primary-btn,
.secondary-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn {
  background: #44403c;
  color: white;
  border: none;
}

.primary-btn:hover {
  background: #57534e;
}

.secondary-btn {
  background: transparent;
  color: #44403c;
  border: 1px solid #d6d3d1;
}

.secondary-btn:hover {
  background: #fafaf9;
}

.ghost-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  color: #78716c;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.ghost-btn:hover {
  background: #f5f5f4;
}

/* Stage-specific colors */
.stage-one {
  background-color: #fbbf24;
  color: #78350f;
}

.stage-two {
  background-color: #c4b5fd;
  color: #581c87;
}

.stage-three {
  background-color: #e7e5e4;
  color: #44403c;
}

.stage-four {
  background-color: #f3f4f6;
  color: #374151;
}

.stage-five {
  background-color: #a3e635;
  color: #365314;
}

.stage-six {
  background-color: #fb923c;
  color: #9a3412;
}

.stage-seven {
  background-color: #92400e;
  color: #fef3c7;
}

/* Responsive design */
@media (max-width: 768px) {
  body {
    padding: 1rem;
  }

  .card.selected {
    transform: translateX(0) scale(1);
    width: 100%;
    max-width: 100%;
  }

  .header h1 {
    font-size: 1.5rem;
  }
}
