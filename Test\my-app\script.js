class StackedCards {
  constructor() {
    this.selectedCard = null
    this.cards = document.querySelectorAll(".card")
    this.init()
  }

  init() {
    // Set initial positions and z-index for cards
    this.cards.forEach((card, index) => {
      card.style.top = `${index * 60}px`
      card.style.zIndex = this.cards.length - index

      // Add click event to card header
      const header = card.querySelector(".card-header")
      header.addEventListener("click", (e) => {
        e.preventDefault()
        const stageId = card.dataset.stage
        this.handleCardClick(stageId)
      })

      // Add click event to close button
      const closeBtn = card.querySelector(".close-btn")
      closeBtn.addEventListener("click", (e) => {
        e.stopPropagation()
        this.closeCard()
      })
    })

    // Set container height
    const container = document.querySelector(".cards-container")
    container.style.height = `${(this.cards.length - 1) * 60 + 500}px`
  }

  handleCardClick(stageId) {
    if (this.selectedCard === stageId) {
      this.closeCard()
    } else {
      this.selectCard(stageId)
    }
  }

  selectCard(stageId) {
    // Close previously selected card
    if (this.selectedCard) {
      this.closeCard()
    }

    this.selectedCard = stageId
    const selectedCardElement = document.querySelector(`[data-stage="${stageId}"]`)

    if (selectedCardElement) {
      // Add selected class
      selectedCardElement.classList.add("selected")

      // Update z-index to bring to front
      selectedCardElement.style.zIndex = "100"

      // Show close button
      const closeBtn = selectedCardElement.querySelector(".close-btn")
      closeBtn.style.display = "flex"

      // Show content with animation
      const content = selectedCardElement.querySelector(".card-content")
      content.style.display = "block"
    }
  }

  closeCard() {
    if (!this.selectedCard) return

    const selectedCardElement = document.querySelector(`[data-stage="${this.selectedCard}"]`)

    if (selectedCardElement) {
      // Remove selected class
      selectedCardElement.classList.remove("selected")

      // Reset z-index
      const index = Number.parseInt(selectedCardElement.dataset.index)
      selectedCardElement.style.zIndex = this.cards.length - index

      // Hide close button
      const closeBtn = selectedCardElement.querySelector(".close-btn")
      closeBtn.style.display = "none"

      // Hide content
      const content = selectedCardElement.querySelector(".card-content")
      setTimeout(() => {
        content.style.display = "none"
      }, 300)
    }

    this.selectedCard = null
  }

  // Public method to get current selected card
  getSelectedCard() {
    return this.selectedCard
  }

  // Public method to select card programmatically
  selectCardById(stageId) {
    this.selectCard(stageId)
  }
}

// Initialize the stacked cards when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.stackedCards = new StackedCards()

  // Optional: Add keyboard support
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && window.stackedCards.getSelectedCard()) {
      window.stackedCards.closeCard()
    }
  })
})

// Optional: Add some utility functions for file actions
function previewFile(fileName) {
  console.log(`预览文件: ${fileName}`)
  // Add your file preview logic here
}

function downloadFile(fileName) {
  console.log(`下载文件: ${fileName}`)
  // Add your file download logic here
}

// Add click events to file action buttons
document.addEventListener("DOMContentLoaded", () => {
  // Add event delegation for file action buttons
  document.addEventListener("click", (e) => {
    if (e.target.classList.contains("action-btn")) {
      const fileItem = e.target.closest(".file-item")
      const fileName = fileItem.querySelector(".file-name").textContent

      // Determine action based on button content
      if (e.target.textContent === "👁") {
        previewFile(fileName)
      } else if (e.target.textContent === "⬇") {
        downloadFile(fileName)
      }
    }
  })
})
