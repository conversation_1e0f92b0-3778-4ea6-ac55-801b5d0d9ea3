{"name": "secure-compare", "version": "3.0.1", "description": "Securely compare two strings, copied from cryptiles", "main": "index.js", "scripts": {"test": "./node_modules/.bin/mocha test"}, "repository": {"type": "git", "url": "https://github.com/vdemedes/secure-compare.git"}, "keywords": ["secure", "compare"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/vdemedes/secure-compare/issues"}, "homepage": "https://github.com/vdemedes/secure-compare", "devDependencies": {"chai": "^2.2.0", "mocha": "^2.2.1"}}