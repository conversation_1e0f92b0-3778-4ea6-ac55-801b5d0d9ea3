{"name": "union", "description": "A hybrid buffered / streaming middleware kernel backwards compatible with connect.", "version": "0.5.0", "author": "<PERSON> <<EMAIL>>", "maintainers": ["dscape <<EMAIL>>"], "repository": {"type": "git", "url": "http://github.com/flatiron/union.git"}, "dependencies": {"qs": "^6.4.0"}, "devDependencies": {"ecstatic": "0.5.x", "director": "1.x.x", "request": "2.29.x", "vows": "0.8.0", "connect": "2.22.x"}, "scripts": {"test": "vows test/*-test.js --spec -i"}, "main": "./lib", "engines": {"node": ">= 0.8.0"}}