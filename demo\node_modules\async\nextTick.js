'use strict';

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _setImmediate = require('./internal/setImmediate.js');

/**
 * Calls `callback` on a later loop around the event loop. In Node.js this just
 * calls `process.nextTick`.  In the browser it will use `setImmediate` if
 * available, otherwise `setTimeout(callback, 0)`, which means other higher
 * priority events may precede the execution of `callback`.
 *
 * This is used internally for browser-compatibility purposes.
 *
 * @name nextTick
 * @static
 * @memberOf module:Utils
 * @method
 * @see [async.setImmediate]{@link module:Utils.setImmediate}
 * @category Util
 * @param {Function} callback - The function to call on a later loop around
 * the event loop. Invoked with (args...).
 * @param {...*} args... - any number of additional arguments to pass to the
 * callback on the next tick.
 * @example
 *
 * var call_order = [];
 * async.nextTick(function() {
 *     call_order.push('two');
 *     // call_order now equals ['one','two']
 * });
 * call_order.push('one');
 *
 * async.setImmediate(function (a, b, c) {
 *     // a, b, and c equal 1, 2, and 3
 * }, 1, 2, 3);
 */
var _defer; /* istanbul ignore file */


if (_setImmediate.hasNextTick) {
    _defer = process.nextTick;
} else if (_setImmediate.hasSetImmediate) {
    _defer = setImmediate;
} else {
    _defer = _setImmediate.fallback;
}

exports.default = (0, _setImmediate.wrap)(_defer);
module.exports = exports.default;